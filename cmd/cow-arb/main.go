package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"math/big"
	"net/http"
	"os"
	"time"

	"github.com/joho/godotenv"
	"github.com/thanhpp/cow-arb/internal/cow-arb/config"
	"github.com/thanhpp/cow-arb/internal/cow-arb/pricing"
	"github.com/thanhpp/cow-arb/internal/cow-arb/storage"
	"github.com/thanhpp/cow-arb/internal/cow-arb/telegram"
	"github.com/thanhpp/cow-arb/internal/cow-arb/worker"
	"github.com/thanhpp/cow-arb/pkg/cow"
	"github.com/thanhpp/cow-arb/pkg/dbutil"
	"github.com/thanhpp/cow-arb/pkg/kyberswap"
	"go.uber.org/fx"
	"go.uber.org/fx/fxevent"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

const (
	ENVPathENV     = "ENV_PATH"
	defaultENVPath = ".env"
)

func main() {
	envPath := os.Getenv(ENVPathENV)
	if len(envPath) == 0 {
		envPath = defaultENVPath
	}
	if err := godotenv.Load(envPath); err != nil {
		log.Println("load env error", err)
	}

	fx.New(
		fx.Provide(NewDB),
		fx.Provide(NewLogger),
		fx.WithLogger(InitLogger),
		fx.Provide(NewTokenStore),
		fx.Provide(NewKSClient),
		fx.Provide(NewCowClient),
		fx.Provide(NewCowSigner),
		fx.Provide(NewKyberswapPricing),
		fx.Provide(worker.NewMonitor),
		fx.Provide(NewConfig),
		fx.Provide(NewHTTPClient),
		fx.Provide(NewArbWorker),
		fx.Provide(NewTelegramBot),
		fx.Invoke(RunArbWorker),
	).Run()
}

func NewDB(lc fx.Lifecycle, cfg config.Config) (*storage.DBStorage, error) {
	l := zap.S().With(
		"host", cfg.Database.Host,
		"port", cfg.Database.Port,
		"user", cfg.Database.User,
		"dbname", cfg.Database.Database,
	)

	l.Infow("Connecting to database...")
	db, err := dbutil.NewDB(dbutil.FormatDSN(map[string]interface{}{
		"host":     cfg.Database.Host,
		"port":     cfg.Database.Port,
		"user":     cfg.Database.User,
		"password": os.Getenv(config.DBPasswordENV),
		"dbname":   cfg.Database.Database,
		"sslmode":  "disable",
	}))
	if err != nil {
		l.Fatalw("Fail to connecting to database", "error", err)
	}

	lc.Append(fx.StartHook(func() error {
		_, err = dbutil.RunMigrationUp(
			db.DB, cfg.Database.MigrationPath,
			cfg.Database.Database,
		)
		if err != nil {
			return fmt.Errorf("migration up: %w", err)
		}

		return nil
	}))

	return storage.NewStorage(db), nil
}

func NewLogger(lc fx.Lifecycle) (*zap.Logger, *zap.SugaredLogger) {
	writers := []io.Writer{os.Stdout}

	w := io.MultiWriter(writers...)
	atom, err := zap.ParseAtomicLevel("DEBUG")
	if err != nil {
		atom = zap.NewAtomicLevelAt(zap.DebugLevel)
	}

	config := zap.NewProductionEncoderConfig()
	config.EncodeTime = zapcore.RFC3339TimeEncoder
	config.CallerKey = "caller"

	encoder := zapcore.NewJSONEncoder(config)
	cc := zap.New(zapcore.NewCore(encoder, zapcore.AddSync(w), atom), zap.AddCaller())
	zap.ReplaceGlobals(cc)

	lc.Append(fx.Hook{
		OnStop: func(_ context.Context) error {
			return cc.Sync()
		},
	})

	return cc, cc.Sugar()
}

func NewTokenStore(lc fx.Lifecycle) *storage.Token {
	tokenStore := storage.NewTokenStore()
	lc.Append(fx.StartHook(func() error {
		return tokenStore.LoadFromJSON(os.Getenv(config.TokenPathENV))
	}))

	return tokenStore
}

func NewKSClient(httpClient *http.Client) *kyberswap.Client {
	return kyberswap.New(httpClient)
}

func NewHTTPClient() *http.Client {
	return &http.Client{
		Timeout: time.Second * 10,
	}
}

func NewCowClient(l *zap.SugaredLogger, httpClient *http.Client, cfg config.Config) *cow.Client {
	return cow.NewCowClient(httpClient, cfg.Cow.BaseURL, l)
}

func NewCowSigner(l *zap.SugaredLogger) (*cow.EIP712Signer, error) {
	return cow.NewEIP712Signer(os.Getenv(config.PrivateKeyENV), l)
}

func RunArbWorker(*worker.ArbWorker) {}

func NewArbWorker(
	_ fx.Lifecycle,
	l *zap.SugaredLogger,
	cfg config.Config,
	price *pricing.Kyberswap,
	store *storage.DBStorage,
	tokens *storage.Token,
	monitor *worker.Monitor,
	tele *telegram.Bot,
	cowClient *cow.Client,
	signer *cow.EIP712Signer,
) *worker.ArbWorker {
	arbWorker := worker.NewArbWorker(
		l,
		worker.Config{
			ChainID:           big.NewInt(int64(cfg.TradePairs[0].ChainID)),
			TradePair:         cfg.TradePairs[0],
			VerifyingContract: cow.CowContractAddress,
			Receiver:          signer.GetAddress(),
		},
		price,
		store,
		tokens,
		monitor,
		tele,
		cowClient,
		signer,
	)

	go arbWorker.Run(context.Background())

	return arbWorker
}

func NewConfig() (config.Config, error) {
	return config.LoadConfig(os.Getenv(config.ConfigPathENV))
}

func InitLogger(lg *zap.Logger) fxevent.Logger {
	return &fxevent.ZapLogger{Logger: lg}
}

func NewKyberswapPricing(cfg config.Config, tokenStore *storage.Token, client *kyberswap.Client) *pricing.Kyberswap {
	return pricing.NewKyberswap(cfg.Kyberswap, tokenStore, client)
}

func NewTelegramBot(lc fx.Lifecycle, cfg config.Config) (*telegram.Bot, error) {
	lc.Append(fx.StartHook(telegram.Init))

	return telegram.New(
		os.Getenv(config.TelegramBotToken),
		cfg.Telegram,
	)
}
