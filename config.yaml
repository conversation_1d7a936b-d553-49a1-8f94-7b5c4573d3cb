database:
  host: "localhost"
  port: 55432
  user: "cowarb"
  database: "cowarb"
  migration_path: "/home/<USER>/workspace/thanhpp/cow-arb/internal/cow-arb/migrations"

receiver: "******************************************" # Example Ethereum address

tradePairs:
  - chainID: 42161 # Arbitrum
    chainCow: "arb1"
    sellToken: "******************************************" # USDC
    buyToken: "******************************************" # USDT
    sellAmount: 666.6

cow:
  base_url: "https://api.cow.fi/arbitrum_one/api/v1"

rpc:
  url: "https://arbitrum.kyberengineering.io"

kyberswap:
  includedSources:
    "arbitrum": arbi-dex,arbidex-v3,arbswap-amm,balancer-v2-composable-stable,balancer-v2-stable,balancer-v2-weighted,balancer-v3-eclp,balancer-v3-stable,balancer-v3-weighted,camelot,camelot-v3,chronos,chronos-v3,curve,curve-lending,curve-stable-meta-ng,curve-stable-ng,curve-stable-plain,curve-tricrypto-ng,curve-twocrypto-ng,dackie-v2,dackie-v3,degen-express,deltaswap-v1,dexalot,dodo-classical,dodo-dpp,dodo-dsp,dodo-dvm,e3,etherfi-ebtc,fluid-dex-t1,fluid-vault-t1,fraxswap,gmx,gyroscope-2clp,gyroscope-3clp,gyroscope-eclp,hashflow-v3,integral,iziswap,ktx,kyberswap,kyberswap-limit-order-v2,kyberswap-static,lo1inch,maverick-v2,mmf,mmf-v3,mummy-finance,native-v1,overnight-usdp,pancake,pancake-stable,pancake-v3,pmm-2,ramses,ramses-v2,sky-psm,smardex,solidly-v3,sparta-dex,sushiswap,sushiswap-v3,swaap-v2,swapr,synapse,thick,traderjoe-v20,traderjoe-v21,traderjoe-v22,uniswap,uniswap-v4,uniswapv3,woofi-v2,woofi-v3,zyberswap-v3

telegram:
  channel: -1002286564789
  threadID: 2
