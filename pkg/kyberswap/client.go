package kyberswap

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/google/go-querystring/query"
	"github.com/thanhpp/cow-arb/pkg/errs"
)

type Client struct {
	hClient  *http.Client
	baseURL  string
	clientID string
}

func New(httpClient *http.Client) *Client {
	c := &Client{
		hClient: httpClient,
		baseURL: PublicBaseURL,
	}

	return c
}

func (c *Client) GetRoute(ctx context.Context, chain string, q GetRouteQueries) (GetRouteResponse, error) {
	if err := q.Validate(); err != nil {
		return GetRouteResponse{}, err
	}

	queries, err := query.Values(q)
	if err != nil {
		return GetRouteResponse{}, fmt.Errorf("form queries error: %w, data: %+v", err, q)
	}
	url := fmt.Sprintf("%s/%s/api/v1/routes?%s", c.baseURL, chain, queries.Encode())

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return GetRouteResponse{}, fmt.Errorf("create new request error: %w", err)
	}

	if len(c.clientID) > 0 {
		req.Header.Add("X-Client-Id", c.clientID)
	}

	var resp GetRouteResponse
	if err := doRequest(c.hClient, req, http.StatusOK, &resp); err != nil {
		return GetRouteResponse{}, fmt.Errorf("do http request error: %w, url: %s", err, url)
	}

	return resp, nil
}

func doRequest(httpClient *http.Client, request *http.Request, expectHTTPCode int, resp any) error {
	hResp, err := httpClient.Do(request)
	if err != nil {
		return fmt.Errorf("do http error: %w", err)
	}
	defer hResp.Body.Close()
	respBody, err := io.ReadAll(hResp.Body)
	if err != nil {
		return fmt.Errorf("read response body error: %w", err)
	}

	if hResp.StatusCode != expectHTTPCode {
		return fmt.Errorf(
			"%w, expect: %d, get: %d, resp: [%s]",
			errs.ErrUnexpectedStatusCode, expectHTTPCode, hResp.StatusCode, string(respBody),
		)
	}

	if err := json.Unmarshal(respBody, resp); err != nil {
		return fmt.Errorf(
			"unmarshal response error: %w, code: [%d], data: [%s]", err, hResp.StatusCode, string(respBody),
		)
	}

	return nil
}
