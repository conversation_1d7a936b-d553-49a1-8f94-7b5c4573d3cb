package kyberswap

import (
	"errors"
	"fmt"
	"math/big"
	"strconv"
)

const (
	PublicBaseURL     = "https://aggregator-api.kyberswap.com"
	PublicMetaBaseURL = "https://meta-aggregator-api.kyberswap.com"
)

const (
	// Contact @tiendv89 to whitelist the caller.
	SourceTrading = "trading"
)

var (
	ErrInvalidQuery  = errors.New("invalid query")
	ErrConvertBigInt = errors.New("error convert big int")
	ErrConvertFloat  = errors.New("error convert float")
)

// https://docs.kyberswap.com/kyberswap-solutions/kyberswap-aggregator/aggregator-api-specification/evm-swaps

type GetRouteQueries struct {
	// required
	TokenIn string `url:"tokenIn"`
	// required
	TokenOut string `url:"tokenOut"`
	// required
	AmountIn string `url:"amountIn"`

	FeeReceiver     string `url:"feeReceiver,omitempty"`
	IsInBps         bool   `url:"isInBps,omitempty"`
	ChargeFeeBy     string `url:"chargeFeeBy,omitempty"`
	FeeAmount       string `url:"feeAmount,omitempty"`
	GasPrice        string `url:"gasPrice,omitempty"`
	GasInclude      bool   `url:"gasInclude,omitempty"`
	ExcludedSources string `url:"excludedSources,omitempty"`
	IncludedSources string `url:"includedSources,omitempty"`
	SaveGas         bool   `url:"saveGas,omitempty"`
	Source          string `url:"source,omitempty"`
}

func (q *GetRouteQueries) Validate() error {
	if len(q.TokenIn) == 0 {
		return fmt.Errorf("%w - empty token in", ErrInvalidQuery)
	}
	if len(q.TokenOut) == 0 {
		return fmt.Errorf("%w - empty token out", ErrInvalidQuery)
	}
	if len(q.AmountIn) == 0 {
		return fmt.Errorf("%w - empty amount in", ErrInvalidQuery)
	}

	if bigAmountIn, _ := new(big.Int).SetString(q.AmountIn, 10); bigAmountIn.Cmp(big.NewInt(0)) < 1 { // nolint
		return fmt.Errorf("%w - zero amount in", ErrInvalidQuery)
	}

	return nil
}

type GetRouteResponse struct {
	Code      int                  `json:"code"`
	Message   string               `json:"message"`
	Data      GetRouteResponseData `json:"data"`
	RequestID string               `json:"requestId"`
}

type GetRouteResponseData struct {
	RouteSummary  RouteSummary `json:"routeSummary"`
	RouterAddress string       `json:"routerAddress"`
}

type RouteSummary struct {
	TokenIn      string    `json:"tokenIn"`
	AmountIn     string    `json:"amountIn"`
	AmountInUsd  string    `json:"amountInUsd"`
	TokenOut     string    `json:"tokenOut"`
	AmountOut    string    `json:"amountOut"`
	AmountOutUsd string    `json:"amountOutUsd"`
	Gas          string    `json:"gas"`
	GasPrice     string    `json:"gasPrice"`
	GasUsd       string    `json:"gasUsd"`
	ExtraFee     ExtraFee  `json:"extraFee"`
	Route        [][]Route `json:"route"`
}

func (r RouteSummary) BigAmountIn() (*big.Int, error) {
	b, ok := new(big.Int).SetString(r.AmountIn, 10)
	if !ok {
		return nil, fmt.Errorf("%w, data: [%s]", ErrConvertBigInt, r.AmountIn)
	}

	return b, nil
}

func (r RouteSummary) FloatAmountInUSD() (float64, error) {
	f, err := strconv.ParseFloat(r.AmountInUsd, 64)
	if err != nil {
		return 0, fmt.Errorf("%w: %w", ErrConvertFloat, err)
	}

	return f, nil
}

func (r RouteSummary) BigAmountOut() (*big.Int, error) {
	b, ok := new(big.Int).SetString(r.AmountOut, 10)
	if !ok {
		return nil, fmt.Errorf("%w, data: [%s]", ErrConvertBigInt, r.AmountOut)
	}

	return b, nil
}

func (r RouteSummary) FloatAmountOutUSD() (float64, error) {
	f, err := strconv.ParseFloat(r.AmountOutUsd, 64)
	if err != nil {
		return 0, fmt.Errorf("parse float error: %w", err)
	}

	return f, nil
}

func (r RouteSummary) BigGas() (*big.Int, error) {
	b, ok := new(big.Int).SetString(r.Gas, 10)
	if !ok {
		return nil, fmt.Errorf("%w, data: [%s]", ErrConvertBigInt, r.Gas)
	}

	return b, nil
}

func (r RouteSummary) BigGasPrice() (*big.Int, error) {
	b, ok := new(big.Int).SetString(r.GasPrice, 10)
	if !ok {
		return nil, fmt.Errorf("%w, data: [%s]", ErrConvertBigInt, r.GasPrice)
	}

	return b, nil
}

func (r RouteSummary) FloatAmountGasUSD() (float64, error) {
	f, err := strconv.ParseFloat(r.GasUsd, 64)
	if err != nil {
		return 0, fmt.Errorf("%w: %w", ErrConvertFloat, err)
	}

	return f, nil
}

type ExtraFee struct {
	FeeAmount   string `json:"feeAmount"`
	ChargeFeeBy string `json:"chargeFeeBy"`
	IsInBps     bool   `json:"isInBps"`
	FeeReceiver string `json:"feeReceiver"`
}

type Route struct {
	Pool              string `json:"pool"`
	TokenIn           string `json:"tokenIn"`
	TokenOut          string `json:"tokenOut"`
	LimitReturnAmount string `json:"limitReturnAmount"`
	SwapAmount        string `json:"swapAmount"`
	AmountOut         string `json:"amountOut"`
	Exchange          string `json:"exchange"`
	PoolLength        int    `json:"poolLength"`
	PoolType          string `json:"poolType"`
	PoolExtra         any    `json:"poolExtra"`
	Extra             any    `json:"extra"`
}
