package cow

import (
	"math/big"

	"github.com/ethereum/go-ethereum/common"
	"go.uber.org/zap"
)

const (
	EmptyHashData      = "0xb48d38f93eaa084033fc5970bf96e559c33c4cdc07d889ab00b4d63f9590739d"
	CowContractAddress = "******************************************"
)

type OrderBuilder struct {
	l                 *zap.SugaredLogger
	signer            *EIP712Signer
	verifyingContract common.Address
	cowClient         *Client
}

func NewOrderBuilder(
	l *zap.SugaredLogger,
	signer *EIP712Signer,
	cowClient *Client,
) *OrderBuilder {
	return &OrderBuilder{
		l:                 l,
		signer:            signer,
		cowClient:         cowClient,
		verifyingContract: common.HexToAddress(CowContractAddress),
	}
}

func (o *OrderBuilder) SubmitOrder(order Order, chainID *big.Int) (string, error) {
	order.AppData = EmptyHashData
	signature, err := o.signer.SignOrder(chainID, o.verifyingContract, order)
	if err != nil {
		o.l.Errorf("cannot get signature", "error", err)
		return "", err
	}
	orderRequest := CreateOrderRequest{
		SellToken:         order.SellToken.String(),
		BuyToken:          order.BuyToken.String(),
		Receiver:          order.Receiver.String(),
		SellAmount:        order.SellAmount.String(),
		BuyAmount:         order.BuyAmount.String(),
		ValidTo:           order.ValidTo.Uint64(),
		FeeAmount:         order.FeeAmount.String(),
		Kind:              order.Kind,
		PartiallyFillable: order.PartiallyFillable,
		SellTokenBalance:  order.SellTokenBalance,
		BuyTokenBalance:   order.BuyTokenBalance,
		SigningScheme:     "eip712",
		Signature:         signature,
		AppData:           "{}",
	}
	uid, err := o.cowClient.CreateOrder(orderRequest)
	if err != nil {
		o.l.Errorw("cannot create order", "error", err)
		return "", err
	}
	return uid, nil
}

type CreateOrderRequest struct {
	SellToken         string `json:"sellToken" form:"sellToken"`
	BuyToken          string `json:"buyToken" form:"buyToken"`
	Receiver          string `json:"receiver" form:"receiver"`
	SellAmount        string `json:"sellAmount" form:"sellAmount"`
	BuyAmount         string `json:"buyAmount" form:"buyAmount"`
	ValidTo           uint64 `json:"validTo" form:"validTo"`
	FeeAmount         string `json:"feeAmount" form:"feeAmount"`
	Kind              string `json:"kind" form:"kind"`
	PartiallyFillable bool   `json:"partiallyFillable" form:"partiallyFillable"`
	SellTokenBalance  string `json:"sellTokenBalance" form:"sellTokenBalance"`
	BuyTokenBalance   string `json:"buyTokenBalance" form:"buyTokenBalance"`
	SigningScheme     string `json:"signingScheme" form:"signingScheme"`
	Signature         string `json:"signature" form:"signature"`
	AppData           string `json:"appData" form:"appData"`
	// From              string `json:"from" form:"from"`
}
