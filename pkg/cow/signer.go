package cow

import (
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/common/math"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/signer/core/apitypes"
	"go.uber.org/zap"
)

const (
	/*
		{
			name: "Gnosis Protocol",
			version: "v2",
			chainId: chain ID for the current network: e.g. 1 for mainnet ,
			verifyingContract: "******************************************"
		}
	*/
	DomainName    = "Gnosis Protocol"
	DomainVersion = "v2"

	EIP712Domain = "EIP712Domain"
	OrderName    = "Order"
)

/*
https://vscode.blockscan.com/ethereum/******************************************
https://etherscan.io/address/******************************************#code#F8#L11

	struct Data {
		IERC20 sellToken;
		IERC20 buyToken;
		address receiver;
		uint256 sellAmount;
		uint256 buyAmount;
		uint32 validTo;
		bytes32 appData;
		uint256 feeAmount;
		bytes32 kind;
		bool partiallyFillable;
		bytes32 sellTokenBalance;
		bytes32 buyTokenBalance;
	}
*/
type Order struct {
	SellToken         common.Address
	BuyToken          common.Address
	Receiver          common.Address
	SellAmount        *big.Int
	BuyAmount         *big.Int
	ValidTo           *big.Int
	AppData           string
	FeeAmount         *big.Int
	Kind              string
	PartiallyFillable bool
	SellTokenBalance  string
	BuyTokenBalance   string
}

/*
	https://vscode.blockscan.com/ethereum/******************************************
	~/Etherscan/GPv2Settlement/src/contracts/libraries/GPv2Order.sol
	https://etherscan.io/address/******************************************#code#F8#L26

	/// @dev The order EIP-712 type hash for the [`GPv2Order.Data`] struct.
	///
	/// This value is pre-computed from the following expression:
	/// ```
	/// keccak256(
	///     "Order(" +
	///         "address sellToken," +
	///         "address buyToken," +
	///         "address receiver," +
	///         "uint256 sellAmount," +
	///         "uint256 buyAmount," +
	///         "uint32 validTo," +
	///         "bytes32 appData," +
	///         "uint256 feeAmount," +
	///         "string kind," +
	///         "bool partiallyFillable" +
	///         "string sellTokenBalance" +
	///         "string buyTokenBalance" +
	///     ")"
	/// )
	/// ```
*/

//nolint:gochecknoglobals
var eip712OrderTypes = apitypes.Types{
	EIP712Domain: []apitypes.Type{
		{Name: "name", Type: "string"},
		{Name: "version", Type: "string"},
		{Name: "chainId", Type: "uint256"},
		{Name: "verifyingContract", Type: "address"},
	},
	OrderName: []apitypes.Type{
		{Name: "sellToken", Type: "address"},
		{Name: "buyToken", Type: "address"},
		{Name: "receiver", Type: "address"},
		{Name: "sellAmount", Type: "uint256"},
		{Name: "buyAmount", Type: "uint256"},
		{Name: "validTo", Type: "uint32"},
		{Name: "appData", Type: "bytes32"},
		{Name: "feeAmount", Type: "uint256"},
		{Name: "kind", Type: "string"},
		{Name: "partiallyFillable", Type: "bool"},
		{Name: "sellTokenBalance", Type: "string"},
		{Name: "buyTokenBalance", Type: "string"},
	},
}

type EIP712Signer struct {
	privateKeyEDCSA *ecdsa.PrivateKey
	l               *zap.SugaredLogger
}

func NewEIP712Signer(privateKey string, l *zap.SugaredLogger) (*EIP712Signer, error) {
	privKey, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return nil, err
	}
	return &EIP712Signer{
		privateKeyEDCSA: privKey,
		l:               l,
	}, err
}

func (s *EIP712Signer) HashOrder(
	chainID *big.Int, verifyingContract common.Address, order Order,
) (common.Hash, error) {
	chainIDHex := math.NewHexOrDecimal256(chainID.Int64())
	domain := apitypes.TypedDataDomain{
		Name:              DomainName,
		Version:           DomainVersion,
		ChainId:           chainIDHex,
		VerifyingContract: verifyingContract.Hex(),
	}
	msg := map[string]any{
		"sellToken":         order.SellToken.Hex(),
		"buyToken":          order.BuyToken.Hex(),
		"receiver":          order.Receiver.Hex(),
		"sellAmount":        order.SellAmount,
		"buyAmount":         order.BuyAmount,
		"validTo":           order.ValidTo,
		"appData":           order.AppData,
		"feeAmount":         order.FeeAmount,
		"kind":              order.Kind,
		"partiallyFillable": order.PartiallyFillable,
		"sellTokenBalance":  order.SellTokenBalance,
		"buyTokenBalance":   order.BuyTokenBalance,
	}
	typedData := apitypes.TypedData{
		Types:       eip712OrderTypes,
		PrimaryType: OrderName,
		Domain:      domain,
		Message:     msg,
	}

	data, _, err := apitypes.TypedDataAndHash(typedData)
	if err != nil {
		return common.Hash{}, fmt.Errorf("hash typed data: %w", err)
	}
	hash := common.BytesToHash(data)

	return hash, nil
}

func (s *EIP712Signer) SignOrder(chainID *big.Int, verifyingContract common.Address, order Order) (string, error) {
	// Sign the digest
	digest, err := s.HashOrder(chainID, verifyingContract, order)
	if err != nil {
		s.l.Errorw("cannot get order hash:", "error", err)
		return "", err
	}
	sig, err := crypto.Sign(digest[:], s.privateKeyEDCSA)
	if err != nil {
		s.l.Errorw("signing failed:", "error", err)
		return "", err
	}
	sig[64] += 27 // adjust v

	s.l.Infoln("Signature:", "0x"+hex.EncodeToString(sig))
	return hexutil.Encode(sig), nil
}

func (s *EIP712Signer) GetAddress() string {
	publicKey := s.privateKeyEDCSA.Public()
	publicKeyECDSA, _ := publicKey.(*ecdsa.PublicKey)

	address := crypto.PubkeyToAddress(*publicKeyECDSA)

	return address.String()
}
