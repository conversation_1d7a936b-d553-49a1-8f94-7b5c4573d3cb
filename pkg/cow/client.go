package cow

import (
	"bytes"
	"fmt"
	"io"
	"net/http"

	"github.com/goccy/go-json"
	"github.com/thanhpp/cow-arb/pkg/errs"
	"go.uber.org/zap"
)

type Client struct {
	client  *http.Client
	l       *zap.SugaredLogger
	baseURL string
}

func NewCowClient(client *http.Client, baseURL string, l *zap.SugaredLogger) *Client {
	return &Client{
		client:  client,
		l:       l,
		baseURL: baseURL,
	}
}

func (c *Client) CreateOrder(order CreateOrderRequest) (string, error) {
	endPoint := "/orders"

	orderJSON, err := json.Marshal(order)
	if err != nil {
		return "", fmt.Errorf("failed to marshal order: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, c.baseURL+endPoint, bytes.NewBuffer(orderJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.client.Do(req)
	if err != nil {
		return "", fmt.<PERSON><PERSON>rf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		data, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", fmt.Errorf("%w, status code: status=%d error=%w", errs.ErrUnexpectedStatusCode, resp.StatusCode, err)
		}
		return "", fmt.Errorf("%w, status code: %d %s", errs.ErrUnexpectedStatusCode, resp.StatusCode, data)
	}
	uid, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	return normalizedUID(string(uid)), nil
}

func (c *Client) GetOrder(uid string) (*OrderResponse, error) {
	endPoint := fmt.Sprintf("/orders/%s", uid)

	resp, err := http.Get(c.baseURL + endPoint) //nolint:noctx
	if err != nil {
		return nil, fmt.Errorf("failed to send GET request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("%w: %d", errs.ErrUnexpectedStatusCode, resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var order OrderResponse
	if err := json.Unmarshal(body, &order); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &order, nil
}

type OrderResponse struct {
	SellToken          string `json:"sellToken"`
	BuyToken           string `json:"buyToken"`
	Receiver           string `json:"receiver"`
	SellAmount         string `json:"sellAmount"`
	BuyAmount          string `json:"buyAmount"`
	ValidTo            uint64 `json:"validTo"`
	FeeAmount          string `json:"feeAmount"`
	Kind               string `json:"kind"`
	PartiallyFillable  bool   `json:"partiallyFillable"`
	SellTokenBalance   string `json:"sellTokenBalance"`
	BuyTokenBalance    string `json:"buyTokenBalance"`
	SigningScheme      string `json:"signingScheme"`
	Signature          string `json:"signature"`
	From               string `json:"from"`
	QuoteID            uint64 `json:"quoteId"`
	AppData            string `json:"appData"`
	AppDataHash        string `json:"appDataHash"`
	CreationDate       string `json:"creationDate"`
	Class              string `json:"class"`
	Owner              string `json:"owner"`
	UID                string `json:"uid"`
	ExecutedSellAmount string `json:"executedSellAmount"`
	ExecutedBuyAmount  string `json:"executedBuyAmount"`
	Status             string `json:"status"`
	IsLiquidityOrder   bool   `json:"isLiquidityOrder"`
	EthflowData        struct {
		RefundTxHash string `json:"refundTxHash"`
		UserValidTo  uint64 `json:"userValidTo"`
	} `json:"ethflowData"`
	OnchainUser      string `json:"onchainUser"`
	OnchainOrderData struct {
		Sender         string `json:"sender"`
		PlacementError string `json:"placementError"`
	} `json:"onchainOrderData"`
	ExecutedFee      string `json:"executedFee"`
	ExecutedFeeToken string `json:"executedFeeToken"`
	FullAppData      string `json:"fullAppData"`
}

func normalizedUID(uid string) string {
	runes := []rune(uid)
	if len(runes) < 2 {
		return uid
	}
	trimmed := string(runes[1 : len(runes)-1])
	return trimmed
}
