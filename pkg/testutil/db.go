package testutil

import (
	"fmt"

	_ "github.com/golang-migrate/migrate/v4/source/file" // nolint go migrate
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq" // nolint sql driver name: "postgres"
	"github.com/thanhpp/cow-arb/pkg/dbutil"
)

// MustNewDevelopmentDB creates a new development DB.
// It also returns a function to teardown it after the test.
func MustNewDevelopmentDB(migrationPath string) (*sqlx.DB, func() error) {
	const dbNameLen = 8

	dbName := RandomString(dbNameLen)
	defaultHost := "127.0.0.1"
	defaultPort := 5432
	defaultUser := "cowarb"
	defaultPassword := "password"

	dsn := dbutil.FormatDSN(map[string]interface{}{
		"host":     defaultHost,
		"port":     defaultPort,
		"user":     defaultUser,
		"password": defaultPassword,
		"sslmode":  "disable",
		"TimeZone": "UTC",
	})

	ddlDB, err := dbutil.NewDB(dsn)
	if err != nil {
		panic(err)
	}
	ddlDB.MustExec(fmt.Sprintf(`CREATE DATABASE "%s"`, dbName))
	if err := ddlDB.Close(); err != nil {
		panic(err)
	}

	dsnWithDB := dbutil.FormatDSN(map[string]interface{}{
		"host":     defaultHost,
		"port":     defaultPort,
		"user":     defaultUser,
		"password": defaultPassword,
		"sslmode":  "disable",
		"dbname":   dbName,
	})

	db, err := dbutil.NewDB(dsnWithDB)
	if err != nil {
		panic(err)
	}

	m, err := dbutil.RunMigrationUp(db.DB, migrationPath, dbName)
	if err != nil {
		panic(err)
	}

	return db, func() error {
		if _, err := m.Close(); err != nil {
			return err
		}
		ddlDB, err := dbutil.NewDB(dsn)
		if err != nil {
			return err
		}
		if _, err = ddlDB.Exec(fmt.Sprintf(`DROP DATABASE "%s"`, dbName)); err != nil {
			return err
		}
		return ddlDB.Close()
	}
}
