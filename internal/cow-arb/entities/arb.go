package entities

import "time"

type ArbStatus string

const (
	ArbStatusSubmit  ArbStatus = "submit"
	ArbStatusCancel  ArbStatus = "cancel"
	ArbStatusSuccess ArbStatus = "success"
)

type Arbitrage struct {
	ID        uint64    `json:"id,omitempty" db:"id"`
	ChainID   int64     `json:"chain_id,omitempty" db:"chain_id"`
	SellToken string    `json:"sell_token,omitempty" db:"sell_token"`
	BuyToken  string    `json:"buy_token,omitempty" db:"buy_token"`
	SellUID   string    `json:"sellUID,omitempty" db:"sell_uid"`
	BuyUID    string    `json:"buyUID,omitempty" db:"buy_uid"`
	Status    ArbStatus `json:"status,omitempty" db:"status"`
	CreatedAt time.Time `json:"createdAt,omitempty" db:"created_at"`
	UpdatedAt time.Time `json:"updatedAt,omitempty" db:"updated_at"`
}
