package entities

import (
	"math/big"
	"time"
)

type OrderKind string

const (
	OrderKindBuy  OrderKind = "buy"
	OrderKindSell OrderKind = "sell"
)

type TradeStatus string

const (
	TradeStatusSubmit TradeStatus = "submit"
	TradeStatusCancel TradeStatus = "cancel"
	TradeStatusFill   TradeStatus = "fill"
)

type Trade struct {
	UID                string `json:"uid,omitempty" db:"uid"`
	SellToken          string `json:"sellToken,omitempty" db:"sell_token"`
	BuyToken           string `json:"buyToken,omitempty" db:"buy_token"`
	Receiver           string `json:"receiver,omitempty" db:"receiver"`
	SellAmount         string `json:"sellAmount,omitempty" db:"sell_amount"`
	BuyAmount          string `json:"buyAmount,omitempty" db:"buy_amount"`
	ExecutedSellAmount string `json:"executedSellAmount" db:"executed_sell_amount"`
	ExecutedBuyAmount  string `json:"executedBuyAmount" db:"executed_buy_amount"`
	ValidTo            string `json:"validTo,omitempty" db:"valid_to"`
	FeeAmount          string `json:"feeAmount,omitempty" db:"fee_amount"`
	Kind               string `json:"kind,omitempty" db:"kind"`
	PartiallyFillable  bool   `json:"partiallyFillable,omitempty" db:"partially_fillable"`
	SellTokenBalance   string `json:"sellTokenBalance,omitempty" db:"sell_token_balance"`
	BuyTokenBalance    string `json:"buyTokenBalance,omitempty" db:"buy_token_balance"`
	Signature          string `json:"signature,omitempty" db:"signature"`

	Status    TradeStatus `json:"status,omitempty" db:"status"`
	CreatedAt time.Time   `json:"createdAt,omitempty" db:"created_at"`
	UpdatedAt time.Time   `json:"updatedAt,omitempty" db:"updated_at"`
}

func (t Trade) IsFilled() bool {
	return t.Status == TradeStatusFill
}

func (t Trade) SellAmountWei() *big.Int {
	wei, _ := new(big.Int).SetString(t.SellAmount, 10)
	return wei
}

func (t Trade) BuyAmountWei() *big.Int {
	wei, _ := new(big.Int).SetString(t.BuyAmount, 10)
	return wei
}

// Handle 0/empty value
func (t Trade) ExecutedSellAmountWei() *big.Int {
	if t.ExecutedSellAmount == "" || t.ExecutedSellAmount == "0" {
		return t.SellAmountWei()
	}

	wei, _ := new(big.Int).SetString(t.ExecutedSellAmount, 10)
	return wei
}

// Handle 0/empty value
func (t Trade) ExecutedBuyAmountWei() *big.Int {
	if t.ExecutedBuyAmount == "" || t.ExecutedBuyAmount == "0" {
		return t.BuyAmountWei()
	}

	wei, _ := new(big.Int).SetString(t.ExecutedBuyAmount, 10)
	return wei
}
