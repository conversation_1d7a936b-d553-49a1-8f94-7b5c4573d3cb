-- Create trades table
CREATE TABLE "trades" (
    "uid" TEXT PRIMARY KEY,
    "sell_token" TEXT NOT NULL,
    "buy_token" TEXT NOT NULL,
    "receiver" TEXT NOT NULL,
    "sell_amount" TEXT NOT NULL,
    "buy_amount" TEXT NOT NULL,
    "valid_to" TEXT NOT NULL,
    "fee_amount" TEXT NOT NULL,
    "kind" VARCHAR(10) NOT NULL,
    "partially_fillable" BOOLEAN NOT NULL,
    "sell_token_balance" TEXT NOT NULL,
    "buy_token_balance" TEXT NOT NULL,
    "signature" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "created_at" TIMESTAMP WITH TIME ZONE NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL
);

CREATE INDEX trades_uid_hash_idx ON trades USING HASH ("uid");

-- Create arbitrages table
CREATE TABLE "arbitrages" (
    "id" BIGSERIAL PRIMARY KEY,
    "chain_id" BIGINT NOT NULL,
    "sell_token" TEXT NOT NULL,
    "buy_token" TEXT NOT NULL,
    "sell_uid" TEXT NOT NULL,
    "buy_uid" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "created_at" TIMESTAMP WITH TIME ZONE NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL
);