package pricing

import (
	"context"
	"errors"
	"fmt"

	"github.com/thanhpp/cow-arb/internal/cow-arb/storage"
	"github.com/thanhpp/cow-arb/pkg/convert"
	"github.com/thanhpp/cow-arb/pkg/kyberswap"
)

var ErrNoDecimals = errors.New("no decimals")

type Kyberswap struct {
	tokenStore *storage.Token
	c          *kyberswap.Client
	config     KyberswapConfig
}

type KyberswapConfig struct {
	IncludedSources map[string]string `yaml:"includedSources"`
}

func NewKyberswap(config KyberswapConfig, tokenStore *storage.Token, client *kyberswap.Client) *Kyberswap {
	return &Kyberswap{
		config:     config,
		tokenStore: tokenStore,
		c:          client,
	}
}

// CalcMidPrice calculates the mid price between two tokens using two getRoute requests
func (k *Kyberswap) CalcMidPrice(
	ctx context.Context, chainID int, tokenA, tokenB string, amountA float64,
) (float64, error) {
	// Get decimals for both tokens
	decimalsA, ok := k.tokenStore.GetDecimals(chainID, tokenA)
	if !ok {
		return 0, fmt.Errorf("%w: %s", ErrNoDecimals, tokenA)
	}
	decimalsB, ok := k.tokenStore.GetDecimals(chainID, tokenB)
	if !ok {
		return 0, fmt.Errorf("%w: %s", ErrNoDecimals, tokenB)
	}

	// Convert amount to wei string based on the source token's decimals
	amountAWei, err := convert.FloatToWei(amountA, decimalsA)
	if err != nil {
		return 0, fmt.Errorf("convert amount: %w, %f", err, amountA)
	}

	// First route request

	sellRouteQ := kyberswap.GetRouteQueries{
		TokenIn:         tokenA,
		TokenOut:        tokenB,
		AmountIn:        amountAWei.String(),
		IncludedSources: k.config.IncludedSources[k.getChainStr(chainID)],
	}
	sellRoute, err := k.c.GetRoute(ctx, k.getChainStr(chainID), sellRouteQ)
	if err != nil {
		return 0, fmt.Errorf("first getRoute: %w", err)
	}
	// Get the output amount from first request
	sellToTokenBAmountWei, err := sellRoute.Data.RouteSummary.BigAmountOut()
	if err != nil {
		return 0, fmt.Errorf("get first amount out: %w", err)
	}
	sellToTokenBAmount := convert.WeiToFloat(sellToTokenBAmountWei, decimalsB)
	rate1 := sellToTokenBAmount / amountA

	// Second route request (reverse direction with output amount)
	buyRouteQ := kyberswap.GetRouteQueries{
		TokenIn:         tokenB,
		TokenOut:        tokenA,
		AmountIn:        sellToTokenBAmountWei.String(),
		IncludedSources: k.config.IncludedSources[k.getChainStr(chainID)],
	}
	buyRoute, err := k.c.GetRoute(ctx, k.getChainStr(chainID), buyRouteQ)
	if err != nil {
		return 0, fmt.Errorf("second getRoute: %w", err)
	}
	buyTokenAAmountWei, err := buyRoute.Data.RouteSummary.BigAmountOut()
	if err != nil {
		return 0, fmt.Errorf("get second amount out: %w", err)
	}
	buyTokenAAmount := convert.WeiToFloat(buyTokenAAmountWei, decimalsA)
	rate2 := sellToTokenBAmount / buyTokenAAmount

	// Calculate mid price
	midPrice := (rate1 + rate2) / 2

	// zap.S().Debugw("calculating mid price", "firstQ", firstRouteQ, "secondQ", secondRouteQ,
	// 	"firstRoute", firstRoute, "secondRoute", secondRoute, "firstAmountOut", firstAmountOut,
	// "secondAmountOut", secondAmountOut,
	// 	"rate1", rate1, "rate2", rate2, "midPrice", midPrice)

	return midPrice, nil
}

func (k *Kyberswap) getChainStr(chainID int) string {
	switch chainID {
	case 1:
		return "ethereum"
	case 8453:
		return "base"
	case 42161:
		return "arbitrum"
	}

	return ""
}
