package config

import (
	"fmt"
	"os"

	"github.com/thanhpp/cow-arb/internal/cow-arb/pricing"
	"github.com/thanhpp/cow-arb/internal/cow-arb/telegram"
	"gopkg.in/yaml.v2"
)

type Config struct {
	Database   Database                `yaml:"database"`
	Receiver   string                  `yaml:"receiver"`
	TradePairs []TradePair             `yaml:"tradePairs"`
	Cow        Cow                     `yaml:"cow"`
	RPC        RPC                     `yaml:"rpc"`
	Kyberswap  pricing.KyberswapConfig `yaml:"kyberswap"`
	Telegram   telegram.Config         `yaml:"telegram"`
}

func LoadConfig(path string) (Config, error) {
	f, err := os.ReadFile(path)
	if err != nil {
		return Config{}, fmt.Errorf("read file: %w, %s", err, path)
	}

	var cfg Config
	if err := yaml.Unmarshal(f, &cfg); err != nil {
		return Config{}, fmt.Errorf("unmarshal config: %w", err)
	}

	return cfg, nil
}

type TradePair struct {
	ChainID    int     `yaml:"chainID"`
	ChainCow   string  `yaml:"chainCow"`
	SellToken  string  `yaml:"sellToken"`
	BuyToken   string  `yaml:"buyToken"`
	SellAmount float64 `yaml:"sellAmount"`
}

type RPC struct {
	URL string `yaml:"url"`
}

type Database struct {
	Host          string `yaml:"host"`
	Port          int    `yaml:"port"`
	User          string `yaml:"user"`
	Database      string `yaml:"database"`
	MigrationPath string `yaml:"migration_path"`
}

type Cow struct {
	BaseURL string `yaml:"base_url"`
}
