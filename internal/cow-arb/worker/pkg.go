package worker

import (
	"context"
	"math/big"

	"github.com/thanhpp/cow-arb/internal/cow-arb/config"
	"github.com/thanhpp/cow-arb/internal/cow-arb/entities"
)

type Pricing interface {
	CalcMidPrice(
		ctx context.Context, chainID int, tokenA, tokenB string, amountA float64,
	) (float64, error)
}

type Storage interface {
	InsertTrade(trade entities.Trade) error
	InsertArbitrage(arb entities.Arbitrage) (entities.Arbitrage, error)

	GetPendingArb(chainID int, sellToken, buyToken string) (entities.Arbitrage, bool, error)

	GetTrade(hash string) (entities.Trade, error)

	UpdateTradeStatus(ctx context.Context, uid string, status entities.TradeStatus, execSellAmt, execBuyAmt string) error
	UpdateArbitrageStatus(ctx context.Context, id uint64, status entities.ArbStatus) error
	UpdateArbBuyUID(ctx context.Context, id uint64, buyUID string) error
	UpdateArbSellUID(ctx context.Context, id uint64, buyUID string) error
}

type TokenStorage interface {
	GetDecimals(chainID int, address string) (int64, bool)
	GetSymbol(chainID int, address string) (string, bool)
}

type IMonitor interface {
	UpdateTradeStatus(ctx context.Context, uid string) (entities.Trade, error)
}

type Config struct {
	ChainID           *big.Int
	TradePair         config.TradePair
	VerifyingContract string
	Receiver          string
	KSIncludedSources string
}
