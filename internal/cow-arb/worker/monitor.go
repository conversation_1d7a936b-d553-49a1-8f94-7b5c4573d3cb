package worker

import (
	"context"
	"fmt"

	"github.com/thanhpp/cow-arb/internal/cow-arb/entities"
	"github.com/thanhpp/cow-arb/internal/cow-arb/storage"
	"github.com/thanhpp/cow-arb/pkg/cow"
	"go.uber.org/zap"
)

type Monitor struct {
	l         *zap.SugaredLogger
	cowClient *cow.Client
	store     Storage
}

func NewMonitor(
	l *zap.SugaredLogger,
	cowClient *cow.Client,
	store *storage.DBStorage,
) *Monitor {
	return &Monitor{
		l:         l,
		cowClient: cowClient,
		store:     store,
	}
}

func (m *Monitor) UpdateTradeStatus(ctx context.Context, uid string) (entities.Trade, error) {
	trade, err := m.store.GetTrade(uid)
	if err != nil {
		return entities.Trade{}, fmt.Errorf("get trade: %w", err)
	}

	switch trade.Status {
	case entities.TradeStatusCancel, entities.TradeStatusFill:
		return trade, nil

	case entities.TradeStatusSubmit:
	}

	order, err := m.cowClient.GetOrder(uid)
	if err != nil {
		return entities.Trade{}, fmt.Errorf("get order: %w", err)
	}

	switch order.Status {
	case "fulfilled":
		trade.Status = entities.TradeStatusFill
		trade.ExecutedSellAmount = order.ExecutedSellAmount
		trade.ExecutedBuyAmount = order.ExecutedBuyAmount
	case "cancelled", "expired":
		trade.Status = entities.TradeStatusCancel
	default:
		return trade, nil
	}

	if err := m.store.UpdateTradeStatus(
		ctx, uid, trade.Status, trade.ExecutedSellAmount, trade.ExecutedBuyAmount,
	); err != nil {
		return entities.Trade{}, err
	}

	return trade, nil
}
