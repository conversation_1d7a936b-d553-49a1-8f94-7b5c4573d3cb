package worker

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/thanhpp/cow-arb/internal/cow-arb/entities"
	"github.com/thanhpp/cow-arb/internal/cow-arb/telegram"
	"github.com/thanhpp/cow-arb/pkg/convert"
	"github.com/thanhpp/cow-arb/pkg/cow"
	"github.com/thanhpp/cow-arb/pkg/errs"
	"go.uber.org/zap"
)

const (
	createInterval         = time.Minute
	orderValidDur          = time.Minute * 5
	keepPriceOrderValidDur = time.Minute * 20 // keep the price for 20 mins
	erc20Balance           = "erc20"
	signingSchemaEIP712    = "eip712"
)

type ArbWorker struct {
	l *zap.SugaredLogger

	cfg     Config
	price   Pricing
	store   Storage
	tokens  TokenStorage
	monitor IMonitor
	tele    *telegram.Bot

	cow    *cow.Client
	signer *cow.EIP712Signer
}

func NewArbWorker(
	l *zap.SugaredLogger,
	cfg Config,
	price Pricing,
	store Storage,
	tokens TokenStorage,
	monitor IMonitor,
	tele *telegram.Bot,
	cow *cow.Client,
	signer *cow.EIP712Signer,
) *ArbWorker {
	return &ArbWorker{
		l:       l,
		cfg:     cfg,
		price:   price,
		store:   store,
		tokens:  tokens,
		monitor: monitor,
		tele:    tele,
		cow:     cow,
		signer:  signer,
	}
}

func (w *ArbWorker) Run(ctx context.Context) {
	t := time.NewTicker(createInterval)
	defer t.Stop()

	for {
		w.run(ctx)

		select {
		case <-ctx.Done():
			return
		case <-t.C:
		}
	}
}

func (w *ArbWorker) run(ctx context.Context) {
	arb, exist, err := w.store.GetPendingArb(
		w.cfg.TradePair.ChainID,
		strings.ToLower(w.cfg.TradePair.SellToken),
		strings.ToLower(w.cfg.TradePair.BuyToken),
	)
	if err != nil {
		w.l.Errorw("get pending arb", "err", err)
		return
	}
	if exist {
		w.l.Infow("got pending", "arb", arb)
	} else {
		arb = entities.Arbitrage{
			ChainID:   w.cfg.ChainID.Int64(),
			SellToken: strings.ToLower(w.cfg.TradePair.SellToken),
			BuyToken:  strings.ToLower(w.cfg.TradePair.BuyToken),
			SellUID:   "",
			BuyUID:    "",
			Status:    entities.ArbStatusSubmit,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		inserted, err := w.store.InsertArbitrage(arb)
		if err != nil {
			w.l.Errorw("insert arb", "err", err, "arb", arb)
			return
		}
		arb.ID = inserted.ID
		w.l.Infow("created new arb", "id", arb.ID)

		_ = w.tele.SendArbCreated(ctx, telegram.ArbCreatedTemplateInput{
			ID:         arb.ID,
			SellToken:  arb.SellToken,
			BuyToken:   arb.BuyToken,
			SellAmount: telegram.FormatFloat(w.cfg.TradePair.SellAmount),
		})
	}

	_, err = w.execArb(ctx, arb)
	if err != nil {
		w.l.Errorw("new arb", "err", err)
		return
	}
}

//nolint:gocognit,funlen
func (w *ArbWorker) execArb(ctx context.Context, arb entities.Arbitrage) (entities.Arbitrage, error) {
	var (
		sellTradeStatus, buyTradeStatus entities.TradeStatus
		sellTrade, buyTrade             entities.Trade
	)
	if arb.SellUID != "" {
		trade, err := w.monitor.UpdateTradeStatus(ctx, arb.SellUID)
		if err != nil {
			return entities.Arbitrage{}, fmt.Errorf("update sell trade: %w", err)
		}
		sellTradeStatus = trade.Status
		sellTrade = trade
		w.l.Infow("sellTradeStatus", "status", sellTradeStatus, "arb", arb.ID, "uid", arb.SellUID)
	}
	if arb.BuyUID != "" {
		trade, err := w.monitor.UpdateTradeStatus(ctx, arb.BuyUID)
		if err != nil {
			return entities.Arbitrage{}, fmt.Errorf("update sell trade: %w", err)
		}
		buyTradeStatus = trade.Status
		buyTrade = trade
		w.l.Infow("buyTradeStatus", "status", buyTradeStatus, "arb", arb.ID, "uid", arb.BuyUID)
	}

	var (
		sellAmountWei, buyAmountWei *big.Int
		err                         error
		validDur                    time.Duration
	)
	switch [2]entities.TradeStatus{sellTradeStatus, buyTradeStatus} {
	case [2]entities.TradeStatus{entities.TradeStatusFill, entities.TradeStatusFill}:
		arb.Status = entities.ArbStatusSuccess
		if err := w.store.UpdateArbitrageStatus(ctx, arb.ID, arb.Status); err != nil {
			return entities.Arbitrage{}, fmt.Errorf("update arb status: %w", err)
		}
		w.l.Infow("arb filled", "arb", arb)
		w.sendArbFilled(ctx, arb, sellTrade, buyTrade)
		return arb, nil // done

	case [2]entities.TradeStatus{entities.TradeStatusSubmit, entities.TradeStatusSubmit}:
		return arb, nil // continue to watch

	case [2]entities.TradeStatus{"", ""}, [2]entities.TradeStatus{entities.TradeStatusCancel, entities.TradeStatusCancel}:
		// nothing filled -> re-calculate amount
		sellAmountWei, buyAmountWei, err = w.calcAmount(ctx)
		if err != nil {
			return entities.Arbitrage{}, fmt.Errorf("calc amount: %w", err)
		}
		validDur = orderValidDur

	// If 1 side is filled, try to keep the price for a while. After that, updates the price to exit position.
	case [2]entities.TradeStatus{entities.TradeStatusFill, entities.TradeStatusCancel},
		[2]entities.TradeStatus{entities.TradeStatusFill, ""}: // sell filled
		if time.Since(sellTrade.UpdatedAt) < keepPriceOrderValidDur {
			sellAmountWei = sellTrade.SellAmountWei()
			buyAmountWei = sellTrade.BuyAmountWei()
			validDur = keepPriceOrderValidDur
			break
		}
		// sell filled
		// buyTrade: sellTrade.ExecBuyFilled -> amt
		buyAmountWei = sellTrade.ExecutedBuyAmountWei()
		sellAmountWei, err = w.calcBuyAmount(
			ctx, arb.BuyToken, arb.SellToken, buyAmountWei.String(),
		)
		if err != nil {
			w.l.Errorw("calc buy amount (sell filled)", "err", err)
			return arb, err
		}
		validDur = orderValidDur
		w.l.Infow("re-calculated amount (sell filled)", "sell", sellAmountWei, "buy", buyAmountWei)

	case [2]entities.TradeStatus{entities.TradeStatusCancel, entities.TradeStatusFill},
		[2]entities.TradeStatus{"", entities.TradeStatusFill}: // buy filled
		if time.Since(buyTrade.UpdatedAt) < keepPriceOrderValidDur {
			sellAmountWei = buyTrade.BuyAmountWei()
			buyAmountWei = buyTrade.SellAmountWei()
			validDur = keepPriceOrderValidDur
			break
		}
		// buy filled. SellTrade: buyTrade.ExecutedBuyAmount -> amt
		sellAmountWei = buyTrade.ExecutedBuyAmountWei()
		buyAmountWei, err = w.calcBuyAmount(
			ctx, arb.SellToken, arb.BuyToken, sellAmountWei.String(),
		)
		if err != nil {
			w.l.Errorw("calc buy amount (sell filled)", "err", err)
			return arb, err
		}
		validDur = orderValidDur
		w.l.Infow("re-calculated amount (buy filled)", "sell", sellAmountWei, "buy", buyAmountWei)
	}

	if arb.SellUID == "" || sellTradeStatus == entities.TradeStatusCancel {
		sellOrder, sellOrderSig, err := w.buildCowSellOrder(sellAmountWei, buyAmountWei, validDur)
		if err != nil {
			return entities.Arbitrage{}, fmt.Errorf("build sell order: %w", err)
		}

		sellTrade, err := w.sendOrderAndSave(sellOrder, sellOrderSig)
		if err != nil {
			return entities.Arbitrage{}, fmt.Errorf("sell and save: %w", err)
		}
		arb.SellUID = sellTrade.UID

		if err := w.store.UpdateArbSellUID(ctx, arb.ID, arb.SellUID); err != nil {
			return entities.Arbitrage{}, fmt.Errorf("update sell uid: %w", err)
		}
	}

	if arb.BuyUID == "" || buyTradeStatus == entities.TradeStatusCancel {
		buyOrder, buyOrderSig, err := w.buildCowBuyOrder(sellAmountWei, buyAmountWei, validDur)
		if err != nil {
			return entities.Arbitrage{}, fmt.Errorf("build buy order: %w", err)
		}

		buyTrade, err := w.sendOrderAndSave(buyOrder, buyOrderSig)
		if err != nil {
			return entities.Arbitrage{}, fmt.Errorf("buy and save: %w", err)
		}
		arb.BuyUID = buyTrade.UID

		if err := w.store.UpdateArbBuyUID(ctx, arb.ID, arb.BuyUID); err != nil {
			return entities.Arbitrage{}, fmt.Errorf("update buy uid: %w", err)
		}
	}

	return arb, nil
}

func (w *ArbWorker) calcAmount(ctx context.Context) (sellAmountWei, buyAmountWei *big.Int, err error) {
	midPrice, err := w.price.CalcMidPrice(
		ctx,
		int(w.cfg.ChainID.Int64()),
		w.cfg.TradePair.SellToken,
		w.cfg.TradePair.BuyToken,
		w.cfg.TradePair.SellAmount,
	)
	if err != nil {
		return nil, nil, fmt.Errorf("calc mid price: %w", err)
	}

	sellDecimals, ok := w.tokens.GetDecimals(int(w.cfg.ChainID.Int64()), w.cfg.TradePair.SellToken)
	if !ok {
		return nil, nil, fmt.Errorf("%w: %s", errs.ErrGetDecimals, w.cfg.TradePair.SellToken)
	}
	buyDecimals, ok := w.tokens.GetDecimals(int(w.cfg.ChainID.Int64()), w.cfg.TradePair.BuyToken)
	if !ok {
		return nil, nil, fmt.Errorf("%w: %s", errs.ErrGetDecimals, w.cfg.TradePair.SellToken)
	}
	sellAmountAfterDecimals, err := convert.FloatToWei(w.cfg.TradePair.SellAmount, sellDecimals)
	if err != nil {
		return nil, nil, fmt.Errorf("sell amount to wei: %w", err)
	}

	buyAmountAfterDecimals := w.cfg.TradePair.SellAmount * midPrice
	buyAmountBigWei, err := convert.FloatToWei(buyAmountAfterDecimals, buyDecimals)
	if err != nil {
		return nil, nil, fmt.Errorf("buy amount to wei: %w", err)
	}

	return sellAmountAfterDecimals, buyAmountBigWei, nil
}

func (w *ArbWorker) calcBuyAmount(
	ctx context.Context,
	sellToken, buyToken string,
	sellAmountWeiString string,
) (buyAmountWei *big.Int, err error) {
	sellDecimals, ok := w.tokens.GetDecimals(int(w.cfg.ChainID.Int64()), sellToken)
	if !ok {
		return nil, fmt.Errorf("%w: %s", errs.ErrGetDecimals, sellToken)
	}
	buyDecimals, ok := w.tokens.GetDecimals(int(w.cfg.ChainID.Int64()), buyToken)
	if !ok {
		return nil, fmt.Errorf("%w: %s", errs.ErrGetDecimals, buyToken)
	}

	sellAmount := convert.WeiStringToFloat(sellAmountWeiString, sellDecimals)
	midPrice, err := w.price.CalcMidPrice(
		ctx,
		int(w.cfg.ChainID.Int64()),
		sellToken,
		buyToken,
		sellAmount,
	)
	if err != nil {
		return nil, fmt.Errorf("calc mid price: %w", err)
	}

	buyAmountAfterDecimals := sellAmount * midPrice
	buyAmountBigWei, err := convert.FloatToWei(buyAmountAfterDecimals, buyDecimals)
	if err != nil {
		return nil, fmt.Errorf("buy amount to wei: %w", err)
	}

	return buyAmountBigWei, nil
}

//nolint:dupl
func (w *ArbWorker) buildCowSellOrder(
	sellAmountWei, buyAmountWei *big.Int,
	dur time.Duration,
) (cow.Order, string, error) {
	order := cow.Order{
		SellToken:         common.HexToAddress(w.cfg.TradePair.SellToken),
		BuyToken:          common.HexToAddress(w.cfg.TradePair.BuyToken),
		Receiver:          common.HexToAddress(w.cfg.Receiver),
		SellAmount:        sellAmountWei,
		BuyAmount:         buyAmountWei,
		ValidTo:           big.NewInt(time.Now().Add(dur).Unix()),
		AppData:           cow.EmptyHashData,
		FeeAmount:         big.NewInt(0),
		Kind:              string(entities.OrderKindSell),
		PartiallyFillable: false,
		SellTokenBalance:  erc20Balance,
		BuyTokenBalance:   erc20Balance,
	}

	sig, err := w.signer.SignOrder(w.cfg.ChainID, common.HexToAddress(w.cfg.VerifyingContract), order)
	if err != nil {
		return cow.Order{}, "", fmt.Errorf("sign order: %w", err)
	}

	return order, sig, nil
}

//nolint:dupl
func (w *ArbWorker) buildCowBuyOrder(
	sellAmountWei, buyAmountWei *big.Int,
	dur time.Duration,
) (cow.Order, string, error) {
	order := cow.Order{
		SellToken:         common.HexToAddress(w.cfg.TradePair.BuyToken),
		BuyToken:          common.HexToAddress(w.cfg.TradePair.SellToken),
		Receiver:          common.HexToAddress(w.cfg.Receiver),
		SellAmount:        buyAmountWei,
		BuyAmount:         sellAmountWei,
		ValidTo:           big.NewInt(time.Now().Add(dur).Unix()),
		AppData:           cow.EmptyHashData,
		FeeAmount:         big.NewInt(0),
		Kind:              string(entities.OrderKindSell),
		PartiallyFillable: false,
		SellTokenBalance:  erc20Balance,
		BuyTokenBalance:   erc20Balance,
	}

	sig, err := w.signer.SignOrder(w.cfg.ChainID, common.HexToAddress(w.cfg.VerifyingContract), order)
	if err != nil {
		return cow.Order{}, "", fmt.Errorf("sign order: %w", err)
	}

	return order, sig, nil
}

func (w *ArbWorker) sendOrderAndSave(order cow.Order, signature string) (entities.Trade, error) {
	orderRequest := cow.CreateOrderRequest{
		SellToken:         order.SellToken.String(),
		BuyToken:          order.BuyToken.String(),
		Receiver:          order.Receiver.String(),
		SellAmount:        order.SellAmount.String(),
		BuyAmount:         order.BuyAmount.String(),
		ValidTo:           order.ValidTo.Uint64(),
		FeeAmount:         order.FeeAmount.String(),
		Kind:              order.Kind,
		PartiallyFillable: order.PartiallyFillable,
		SellTokenBalance:  order.SellTokenBalance,
		BuyTokenBalance:   order.BuyTokenBalance,
		SigningScheme:     signingSchemaEIP712,
		Signature:         signature,
		AppData:           "{}",
	}

	uid, err := w.cow.CreateOrder(orderRequest)
	if err != nil {
		return entities.Trade{}, fmt.Errorf("create order: %w", err)
	}

	w.l.Infow("order created", "req", orderRequest, "uid", uid)

	trade := entities.Trade{
		UID:                uid,
		SellToken:          strings.ToLower(order.SellToken.String()),
		BuyToken:           strings.ToLower(order.BuyToken.String()),
		Receiver:           strings.ToLower(order.Receiver.String()),
		SellAmount:         strings.ToLower(order.SellAmount.String()),
		BuyAmount:          strings.ToLower(order.BuyAmount.String()),
		ExecutedSellAmount: "0",
		ExecutedBuyAmount:  "0",
		ValidTo:            strings.ToLower(order.ValidTo.String()),
		FeeAmount:          strings.ToLower(order.FeeAmount.String()),
		Kind:               order.Kind,
		PartiallyFillable:  order.PartiallyFillable,
		SellTokenBalance:   order.SellTokenBalance,
		BuyTokenBalance:    order.BuyTokenBalance,
		Signature:          signature,

		Status:    entities.TradeStatusSubmit,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return trade, w.store.InsertTrade(trade)
}

func (w *ArbWorker) sendArbFilled(ctx context.Context, arb entities.Arbitrage, sell, buy entities.Trade) {
	sellSb, ok := w.tokens.GetSymbol(w.cfg.TradePair.ChainID, arb.SellToken)
	if !ok {
		sellSb = arb.SellToken
	}
	buySb, ok := w.tokens.GetSymbol(w.cfg.TradePair.ChainID, arb.BuyToken)
	if !ok {
		buySb = arb.BuyToken
	}
	sellDec, ok := w.tokens.GetDecimals(w.cfg.TradePair.ChainID, arb.SellToken)
	if !ok {
		sellDec = 1
	}
	buyDec, ok := w.tokens.GetDecimals(w.cfg.TradePair.ChainID, arb.BuyToken)
	if !ok {
		buyDec = 1
	}

	if err := w.tele.SendArbFilled(
		ctx,
		telegram.ArbFilledTemplateInput{
			ID:             arb.ID,
			SellToken:      sellSb,
			BuyToken:       buySb,
			SellFromAmount: telegram.FormatFloat(convert.WeiToFloat(sell.ExecutedSellAmountWei(), sellDec)),
			SellFromToken:  sellSb,
			SellToAmount:   telegram.FormatFloat(convert.WeiToFloat(sell.ExecutedBuyAmountWei(), buyDec)),
			SellToToken:    buySb,
			BuyFromAmount:  telegram.FormatFloat(convert.WeiToFloat(buy.ExecutedSellAmountWei(), buyDec)),
			BuyFromToken:   buySb,
			BuyToAmount:    telegram.FormatFloat(convert.WeiToFloat(buy.ExecutedBuyAmountWei(), sellDec)),
			BuyToToken:     sellSb,
			Chain:          w.cfg.TradePair.ChainCow,
			SellTradeUID:   arb.SellUID,
			BuyTradeUID:    arb.BuyUID,
		},
	); err != nil {
		w.l.Errorw("send arb filled", "err", err)
	}
}
