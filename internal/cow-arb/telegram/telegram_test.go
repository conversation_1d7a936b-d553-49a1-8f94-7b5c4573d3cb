package telegram_test

import (
	"testing"

	"github.com/test-go/testify/require"
	"github.com/thanhpp/cow-arb/internal/cow-arb/telegram"
)

func TestSendArbCreated(t *testing.T) {
	t.Skip("need bot token")
	var (
		channel  = -1002677785948 // Replace with your test channel ID
		threadID = -1
		token    = "...."
	)
	require.NotEmpty(t, token, "TELEGRAM_BOT_TOKEN environment variable is required")

	// Initialize templates
	err := telegram.Init()
	require.NoError(t, err)

	// Create bot instance
	bot, err := telegram.New(token, telegram.Config{
		Channel:  channel,
		ThreadID: threadID,
	})
	require.NoError(t, err)

	// Create test input
	input := telegram.ArbCreatedTemplateInput{
		ID:         999999,
		SellToken:  "USDC",
		BuyToken:   "WETH",
		SellAmount: telegram.FormatFloat(1000), // 1000 USDC with 6 decimals
	}

	// Send message
	err = bot.SendArbCreated(t.Context(), input)
	require.NoError(t, err)
}

func TestSendArbFilled(t *testing.T) {
	t.Skip("need bot token")
	var (
		channel  = -1002677785948 // Replace with your test channel ID
		threadID = -1
		token    = "7779929335:..." //nolint:gosec
	)
	require.NotEmpty(t, token, "TELEGRAM_BOT_TOKEN environment variable is required")

	// Initialize templates
	err := telegram.Init()
	require.NoError(t, err)

	// Create bot instance
	bot, err := telegram.New(token, telegram.Config{
		Channel:  channel,
		ThreadID: threadID,
	})
	require.NoError(t, err)

	// Create test input
	input := telegram.ArbFilledTemplateInput{
		ID:             999999,
		SellToken:      "USDC",
		BuyToken:       "WETH",
		SellFromAmount: telegram.FormatFloat(1000000000), // 1000 USDC
		SellFromToken:  "USDC",
		SellToAmount:   telegram.FormatFloat(0.5),
		SellToToken:    "WETH",
		BuyFromAmount:  telegram.FormatFloat(0.5),
		BuyFromToken:   "WETH",
		BuyToAmount:    telegram.FormatFloat(1010000000), // 1010 USDC
		BuyToToken:     "USDC",
		Chain:          "arb1",
		SellTradeUID:   "0x79ff9c23ae1c4e5d9fa09887bf52239cbbf9d6c72e8133c002e69dc800cedf9744c8cf41ec81fbd259abb6b9bb07da43ea55deee684055e1",
		BuyTradeUID:    "0x79ff9c23ae1c4e5d9fa09887bf52239cbbf9d6c72e8133c002e69dc800cedf9744c8cf41ec81fbd259abb6b9bb07da43ea55deee684055e1",
	}

	// Send message
	err = bot.SendArbFilled(t.Context(), input)
	require.NoError(t, err)
}
