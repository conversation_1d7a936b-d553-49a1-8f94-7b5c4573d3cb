package telegram

import (
	"fmt"
	"strconv"
	"strings"
	"text/template"
)

type Config struct {
	Channel  int `yaml:"channel"`
	ThreadID int `yaml:"threadID"`
}

const (
	ArbCreatedTemplateText = `Arb created
ID: {{.ID}}
SellToken: {{.SellToken}}
BuyToken: {{.BuyToken}}
SellAmount: {{.SellAmount}}
`
	//nolint:lll
	ArbFilledTemplateText = `Arb filled
ID: {{.ID}}
SellToken: {{.SellToken}}
BuyToken: {{.BuyToken}}
[SellTrade](https://explorer.cow.fi/{{.Chain}}/orders/{{.SellTradeUID}}): {{.SellFromAmount}} {{.SellFromToken}} \-\> {{.SellToAmount}} {{.SellToToken}}
[BuyTrade](https://explorer.cow.fi/{{.Chain}}/orders/{{.BuyTradeUID}}): {{.BuyFromAmount}} {{.BuyFromToken}} \-\> {{.BuyToAmount}} {{.BuyToToken}}
`
)

type ArbCreatedTemplateInput struct {
	ID         uint64
	SellToken  string
	BuyToken   string
	SellAmount FormatedFloat
}

type ArbFilledTemplateInput struct {
	ID             uint64
	SellToken      string
	BuyToken       string
	SellFromAmount FormatedFloat
	SellFromToken  string
	SellToAmount   FormatedFloat
	SellToToken    string
	BuyFromAmount  FormatedFloat
	BuyFromToken   string
	BuyToAmount    FormatedFloat
	BuyToToken     string

	Chain        string
	SellTradeUID string
	BuyTradeUID  string
}

var (
	arbCreatedTemplate *template.Template
	arbFilledTemplate  *template.Template
)

func Init() error {
	{
		parsed, err := template.New("arb-created").Parse(ArbCreatedTemplateText)
		if err != nil {
			return fmt.Errorf("parse arb created: %w", err)
		}
		arbCreatedTemplate = parsed
	}

	{
		parsed, err := template.New("arb-filled").Parse(ArbFilledTemplateText)
		if err != nil {
			return fmt.Errorf("parse arb filled: %w", err)
		}
		arbFilledTemplate = parsed
	}

	return nil
}

type FormatedFloat = string

func FormatFloat(f64 float64) FormatedFloat {
	str := strconv.FormatFloat(f64, 'f', 5, 64)
	str = strings.ReplaceAll(str, "+", "\\+")
	str = strings.ReplaceAll(str, "-", "\\-")
	str = strings.ReplaceAll(str, ".", "\\.")

	return str
}
