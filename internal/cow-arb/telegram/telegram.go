package telegram

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

type Bot struct {
	b   *bot.Bot
	cfg Config
}

func New(token string, cfg Config) (*Bot, error) {
	bot, err := bot.New(token)
	if err != nil {
		return nil, fmt.Errorf("new bot api: %w", err)
	}

	return &Bot{
		b:   bot,
		cfg: cfg,
	}, nil
}

func (b *Bot) SendArbCreated(ctx context.Context, input ArbCreatedTemplateInput) error {
	var output strings.Builder
	if err := arbCreatedTemplate.Execute(&output, input); err != nil {
		return fmt.Errorf("execute arb created template: %w", err)
	}

	_, err := b.b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:          b.cfg.Channel,
		MessageThreadID: b.cfg.ThreadID,
		Text:            output.String(),
		ParseMode:       models.ParseModeMarkdown,
	})
	if err != nil {
		return fmt.Errorf("send msg: %w", err)
	}

	return nil
}

func (b *Bot) SendArbFilled(ctx context.Context, input ArbFilledTemplateInput) error {
	var output strings.Builder
	if err := arbFilledTemplate.Execute(&output, input); err != nil {
		return fmt.Errorf("execute arb filled template: %w", err)
	}

	_, err := b.b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:             b.cfg.Channel,
		MessageThreadID:    b.cfg.ThreadID,
		Text:               output.String(),
		ParseMode:          models.ParseModeMarkdown,
		LinkPreviewOptions: &models.LinkPreviewOptions{IsDisabled: bot.True()},
	})
	if err != nil {
		return fmt.Errorf("send msg: %w, %s", err, output.String())
	}

	return nil
}
