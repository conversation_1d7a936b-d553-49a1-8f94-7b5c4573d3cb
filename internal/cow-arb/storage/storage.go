package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"github.com/thanhpp/cow-arb/internal/cow-arb/entities"
)

var ErrNotFound = errors.New("not found")

type DBStorage struct {
	db *sqlx.DB
}

func NewStorage(db *sqlx.DB) *DBStorage {
	return &DBStorage{db: db}
}

func (s *DBStorage) InsertTrade(trade entities.Trade) error {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Insert("trades").
		Columns(
			"uid",
			"sell_token",
			"buy_token",
			"receiver",
			"sell_amount",
			"buy_amount",
			"executed_sell_amount",
			"executed_buy_amount",
			"valid_to",
			"fee_amount",
			"kind",
			"partially_fillable",
			"sell_token_balance",
			"buy_token_balance",
			"signature",
			"status",
			"created_at",
			"updated_at",
		).
		Values(
			trade.UID,
			trade.SellToken,
			trade.BuyToken,
			trade.Receiver,
			trade.SellAmount,
			trade.BuyAmount,
			trade.ExecutedSellAmount,
			trade.ExecutedBuyAmount,
			trade.ValidTo,
			trade.FeeAmount,
			trade.Kind,
			trade.PartiallyFillable,
			trade.SellTokenBalance,
			trade.BuyTokenBalance,
			trade.Signature,
			trade.Status,
			trade.CreatedAt,
			trade.UpdatedAt,
		).
		ToSql()
	if err != nil {
		return fmt.Errorf("build insert trade query: %w", err)
	}

	_, err = s.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("exec insert trade: %w", err)
	}

	return nil
}

func (s *DBStorage) InsertArbitrage(arb entities.Arbitrage) (entities.Arbitrage, error) {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Insert("arbitrages").
		Columns(
			"chain_id",
			"sell_token",
			"buy_token",
			"sell_uid",
			"buy_uid",
			"status",
			"created_at",
			"updated_at",
		).
		Values(
			arb.ChainID,
			arb.SellToken,
			arb.BuyToken,
			arb.SellUID,
			arb.BuyUID,
			arb.Status,
			arb.CreatedAt,
			arb.UpdatedAt,
		).
		Suffix("RETURNING id").
		ToSql()
	if err != nil {
		return entities.Arbitrage{}, fmt.Errorf("build insert arbitrage query: %w", err)
	}

	// Use QueryRow to get the returned ID
	var id uint64
	err = s.db.QueryRow(query, args...).Scan(&id)
	if err != nil {
		return entities.Arbitrage{}, fmt.Errorf("exec insert arbitrage: %w", err)
	}

	arb.ID = id
	return arb, nil
}

func (s *DBStorage) GetTrade(uid string) (entities.Trade, error) {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Select(
		"*",
	).
		From("trades").
		Where(sq.Eq{"uid": uid}).
		ToSql()
	if err != nil {
		return entities.Trade{}, fmt.Errorf("build select trade query: %w", err)
	}

	var trade entities.Trade
	err = s.db.Get(&trade, query, args...)
	if err != nil {
		return entities.Trade{}, fmt.Errorf("get trade: %w", err)
	}

	return trade, nil
}

func (s *DBStorage) GetArbitragesByStatus(status entities.ArbStatus) ([]entities.Arbitrage, error) {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Select(
		"*",
	).
		From("arbitrages").
		Where(sq.Eq{"status": status}).
		ToSql()
	if err != nil {
		return nil, fmt.Errorf("build select arbitrages query: %w", err)
	}

	var arbitrages []entities.Arbitrage
	err = s.db.Select(&arbitrages, query, args...)
	if err != nil {
		return nil, fmt.Errorf("select arbitrages: %w", err)
	}

	return arbitrages, nil
}

func (s *DBStorage) GetPendingArb(chainID int, sellToken, buyToken string) (entities.Arbitrage, bool, error) {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)

	// Join with trades table to get the token information
	query, args, err := psql.Select(
		"a.*",
	).
		From("arbitrages a").
		Where(sq.And{
			sq.Eq{"a.chain_id": chainID},
			sq.Eq{"a.sell_token": sellToken},
			sq.Eq{"a.buy_token": buyToken},
			sq.Or{sq.Eq{"a.status": entities.ArbStatusSubmit}},
		}).
		OrderBy("a.created_at DESC").
		Limit(1).
		ToSql()
	if err != nil {
		return entities.Arbitrage{}, false, fmt.Errorf("build select pending arb query: %w", err)
	}

	var arb entities.Arbitrage
	err = s.db.Get(&arb, query, args...)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return entities.Arbitrage{}, false, nil
		}
		return entities.Arbitrage{}, false, fmt.Errorf("get pending arb: %w", err)
	}

	return arb, true, nil
}

func (s *DBStorage) UpdateArbitrageStatus(ctx context.Context, id uint64, status entities.ArbStatus) error {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Update("arbitrages").
		Set("status", status).
		Set("updated_at", time.Now()).
		Where(sq.Eq{"id": id}).
		ToSql()
	if err != nil {
		return fmt.Errorf("build update arbitrage status query: %w", err)
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("exec update arbitrage status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("%w: %d", ErrNotFound, id)
	}

	return nil
}

func (s *DBStorage) UpdateArbBuyUID(ctx context.Context, id uint64, buyUID string) error {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Update("arbitrages").
		Set("buy_uid", buyUID).
		Set("updated_at", time.Now()).
		Where(sq.Eq{"id": id}).
		ToSql()
	if err != nil {
		return fmt.Errorf("build update buy_uid query: %w", err)
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("exec update buy_uid: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("%w: %d", ErrNotFound, id)
	}

	return nil
}

func (s *DBStorage) UpdateArbSellUID(ctx context.Context, id uint64, sellUID string) error {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Update("arbitrages").
		Set("sell_uid", sellUID).
		Set("updated_at", time.Now()).
		Where(sq.Eq{"id": id}).
		ToSql()
	if err != nil {
		return fmt.Errorf("build update buy_uid query: %w", err)
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("exec update buy_uid: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("%w: %d", ErrNotFound, id)
	}

	return nil
}

func (s *DBStorage) UpdateTradeStatus(
	ctx context.Context, uid string, status entities.TradeStatus,
	execSellAmount, execBuyAmount string,
) error {
	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	query, args, err := psql.Update("trades").
		Set("status", status).
		Set("executed_sell_amount", execSellAmount).
		Set("executed_buy_amount", execBuyAmount).
		Set("updated_at", time.Now()).
		Where(sq.Eq{"uid": uid}).
		ToSql()
	if err != nil {
		return fmt.Errorf("build update trade status query: %w", err)
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("exec update trade status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("%w: %s", ErrNotFound, uid)
	}

	return nil
}
