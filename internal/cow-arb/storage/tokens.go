package storage

import (
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"

	"github.com/goccy/go-json"
	"github.com/thanhpp/cow-arb/internal/cow-arb/entities"
)

var ErrTokenNotFound = errors.New("token not found")

// Token provides thread-safe in-memory storage for token data
type Token struct {
	rw     sync.RWMutex
	tokens map[int]map[string]entities.Token // chainID -> address -> Token
}

// NewTokenStore creates a new TokenStore instance
func NewTokenStore() *Token {
	return &Token{
		tokens: make(map[int]map[string]entities.Token),
	}
}

// LoadFromJSON loads token data from a JSON file
func (t *Token) LoadFromJSON(filepath string) error {
	data, err := os.ReadFile(filepath)
	if err != nil {
		return fmt.Errorf("read token file: %w", err)
	}

	var tokens []entities.Token
	if err := json.Unmarshal(data, &tokens); err != nil {
		return fmt.Errorf("unmarshal tokens: %w", err)
	}

	t.rw.Lock()
	defer t.rw.Unlock()

	// Reset the existing data
	t.tokens = make(map[int]map[string]entities.Token)

	// Store tokens organized by chainID and address
	for _, token := range tokens {
		if _, exist := t.tokens[token.ChainID]; !exist {
			t.tokens[token.ChainID] = make(map[string]entities.Token)
		}
		t.tokens[token.ChainID][strings.ToLower(token.Address)] = token
	}

	return nil
}

// SetToken adds or updates a token in the store
func (t *Token) SetToken(token entities.Token) {
	t.rw.Lock()
	defer t.rw.Unlock()

	t.tokens[token.ChainID][strings.ToLower(token.Address)] = token
}

// GetToken retrieves a token by chainID and address
func (t *Token) GetToken(chainID int, address string) (entities.Token, bool) {
	t.rw.RLock()
	defer t.rw.RUnlock()

	if chainTokens, exists := t.tokens[chainID]; exists {
		if token, found := chainTokens[address]; found {
			return token, true
		}
	}
	return entities.Token{}, false
}

// GetDecimals retrieves the decimals for a token by address
func (t *Token) GetDecimals(chainID int, address string) (int64, bool) {
	t.rw.RLock()
	defer t.rw.RUnlock()

	// Search through all chains since we don't have chainID in this interface
	if chainTokens, exist := t.tokens[chainID]; exist {
		if token, exists := chainTokens[strings.ToLower(address)]; exists {
			return int64(token.Decimals), true
		}
	}

	return 0, false
}

func (t *Token) GetSymbol(chainID int, address string) (string, bool) {
	t.rw.RLock()
	defer t.rw.RUnlock()

	// Search through all chains since we don't have chainID in this interface
	if chainTokens, exist := t.tokens[chainID]; exist {
		if token, exists := chainTokens[strings.ToLower(address)]; exists {
			return token.Symbol, true
		}
	}

	return "", false
}
