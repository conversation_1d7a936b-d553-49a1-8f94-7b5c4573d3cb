version: "2"
linters:
  default: all
  disable:
    - contextcheck
    - copyloopvar
    - cyclop
    - depguard
    - exhaustruct
    - fatcontext
    - godox
    - gomoddirectives
    - interfacebloat
    - intrange
    - ireturn
    - mnd
    - nlreturn
    - nolintlint
    - nonamedreturns
    - paralleltest
    - perfsprint
    - protogetter
    - rowserrcheck
    - spancheck
    - sqlclosecheck
    - tagalign
    - tagliatelle
    - testifylint
    - testpackage
    - varnamelen
    - wastedassign
    - whitespace
    - wrapcheck
    - wsl
    - funcorder
    - recvcheck
    - noctx
    - godot
    - gochecknoglobals
  settings:
    funlen:
      lines: 90
      statements: 60
      ignore-comments: true
    gosec:
      excludes:
        - G115
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - funlen
          - gocognit
          - lll
        path: _test\.go
      - linters:
          - lll
        source: "^// "
    paths:
      - onchain-price
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - gci
    - gofmt
    - gofumpt
    - goimports
  exclusions:
    generated: lax
    paths:
      - onchain-price
      - third_party$
      - builtin$
      - examples$
