// nolint
package service

import (
	"context"
	"crypto/ecdsa"
	"errors"
	"fmt"
	"log"
	"math/big"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/syndtr/goleveldb/leveldb"
	"gitlab.com/nvhbk16k53/cow-arb/internal/blockchain"
	"gitlab.com/nvhbk16k53/cow-arb/internal/cow"
	"gitlab.com/nvhbk16k53/cow-arb/internal/pricing"
)

const (
	defaultOrderDuration = 10 * time.Minute
	balanceSourceERC20   = "erc20"
	signingSchemeEIP712  = "eip712"
	appDataEmpty         = "{}"

	orderStatusOpen      = "open"
	orderStatusFulfilled = "fulfilled"
)

type PairConfig struct {
	Key         string
	Account     common.Address
	Token1      string
	Token2      string
	StartAmount float64
	MinRate     float64
	MaxRate     float64
}

type Config struct {
	ChainID            *big.Int
	SettlementContract common.Address
	Pairs              []PairConfig
}

type Service struct {
	cfg         Config
	privateKeys map[common.Address]*ecdsa.PrivateKey
	erc20       *blockchain.ERC20
	cowClient   *cow.Client
	pricer      *pricing.Service
	db          *leveldb.DB
}

func New(
	cfg Config,
	pkeys []*ecdsa.PrivateKey,
	erc20 *blockchain.ERC20,
	cowClient *cow.Client,
	pricer *pricing.Service,
	db *leveldb.DB,
) *Service {
	privateKeys := make(map[common.Address]*ecdsa.PrivateKey)
	for _, pkey := range pkeys {
		publicKey := pkey.Public()
		publicKeyECDSA, _ := publicKey.(*ecdsa.PublicKey)
		address := crypto.PubkeyToAddress(*publicKeyECDSA)
		privateKeys[address] = pkey
	}

	for _, pair := range cfg.Pairs {
		if _, ok := privateKeys[pair.Account]; !ok {
			panic("missing private key for account: " + pair.Account.Hex())
		}
	}

	return &Service{
		cfg:         cfg,
		privateKeys: privateKeys,
		erc20:       erc20,
		cowClient:   cowClient,
		pricer:      pricer,
		db:          db,
	}
}

func (s *Service) Run(ctx context.Context) {
	var wg sync.WaitGroup
	for _, p := range s.cfg.Pairs {
		wg.Add(1)
		go func(pair PairConfig) {
			defer wg.Done()
			s.RunArb(ctx, pair)
		}(p)
	}
	wg.Wait()
}

func (s *Service) runArb(ctx context.Context, pair PairConfig) error {
	tokens := []string{pair.Token1, pair.Token2}
	decimals, err := s.getDecimalsForTokens(ctx, tokens)
	if err != nil {
		log.Printf("Fail to get decimals for tokens %v: %v\n", tokens, err)
		return err
	}

	startAmount := mustFloatToWei(pair.StartAmount, decimals[0])
	rates, err := s.getRates(ctx, tokens, decimals, startAmount, pair.MinRate, pair.MaxRate)
	if err != nil {
		log.Printf("Fail to get rates %+v: %v", pair, err)
		return err
	}

	// Get last saved orderID from database if exists.
	orderID, err := s.getOrderID(pair.Key)
	if err != nil {
		log.Printf("Fail to get orderID from database for key %s: %v\n", pair.Key, err)
		return err
	}
	log.Printf("Last saved orderID for key %s: %s\n", pair.Key, orderID)

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	for {
		orderID, err = s.checkAndCreateOrder(
			ctx, pair.Key, orderID, pair.Account, tokens, decimals,
			rates, startAmount, pair.MinRate, pair.MaxRate)
		if err != nil {
			log.Printf("Fail to check and create order: orderID=%s pair=%+v error=%v\n", orderID, pair, err)
			return err
		}

		select {
		case <-ctx.Done():
			log.Printf("Stop running arbitrage bot: %+v\n", pair)
			return nil
		case <-ticker.C:
		}
	}
}

func (s *Service) RunArb(ctx context.Context, pair PairConfig) {
	for {
		err := s.runArb(ctx, pair)
		if err == nil || errors.Is(err, context.Canceled) {
			return
		}

		log.Printf("Error occur while running arbitrage strategy for pair %+v, retry...\n", pair)
	}
}

func (s *Service) CreateOrder(
	ctx context.Context,
	account common.Address,
	sellToken, buyToken string,
	sellAmount, buyAmount *big.Int,
	kind string,
	partiallyFillable bool,
) (string, error) {
	orderToSign := blockchain.Order{
		SellToken:         sellToken,
		BuyToken:          buyToken,
		Receiver:          account.Hex(),
		SellAmount:        sellAmount.String(),
		BuyAmount:         buyAmount.String(),
		ValidTo:           time.Now().Add(defaultOrderDuration).Unix(),
		AppData:           cow.DefaultAppDataHash,
		FeeAmount:         "0",
		Kind:              kind,
		PartiallyFillable: partiallyFillable,
		SellTokenBalance:  balanceSourceERC20,
		BuyTokenBalance:   balanceSourceERC20,
	}
	sig, err := blockchain.SignOrder(
		s.privateKeys[account], s.cfg.ChainID, s.cfg.SettlementContract, orderToSign)
	if err != nil {
		log.Printf("Fail to sign order: order=%+v error=%v\n", orderToSign, err)
		return "", err
	}

	req := cow.CreateOrderRequest{
		SellToken:         orderToSign.SellToken,
		BuyToken:          orderToSign.BuyToken,
		Receiver:          orderToSign.Receiver,
		SellAmount:        orderToSign.SellAmount,
		BuyAmount:         orderToSign.BuyAmount,
		ValidTo:           orderToSign.ValidTo,
		FeeAmount:         orderToSign.FeeAmount,
		Kind:              orderToSign.Kind,
		PartiallyFillable: orderToSign.PartiallyFillable,
		SellTokenBalance:  orderToSign.SellTokenBalance,
		BuyTokenBalance:   orderToSign.BuyTokenBalance,
		SigningScheme:     signingSchemeEIP712,
		Signature:         hexutil.Encode(sig),
		From:              account.Hex(),
		AppData:           appDataEmpty,
	}
	log.Printf("Create order: %+v\n", req)
	orderID, err := s.cowClient.CreateOrder(ctx, req)
	if err != nil {
		log.Printf("Fail to create order: request=%+v error=%v\n", req, err)
		return "", err
	}
	log.Printf("Create order success: orderID=%s\n", orderID)

	return orderID, nil
}

//nolint:unused
func (s *Service) getDecimalsForTokens(ctx context.Context, tokens []string) ([]uint8, error) {
	var (
		err      error
		decimals = make([]uint8, len(tokens))
	)
	for i, token := range tokens {
		decimals[i], err = s.erc20.Decimals(ctx, common.HexToAddress(token))
		if err != nil {
			log.Printf("Get decimals: token=%s error=%v", token, err)
			return nil, fmt.Errorf("get decimals for tokens %s: %w", token, err)
		}
	}

	return decimals, nil
}

//nolint:unused
func (s *Service) getBalanceForTokens(
	ctx context.Context, account common.Address, tokens []string,
) ([]*big.Int, error) {
	var (
		err      error
		balances = make([]*big.Int, len(tokens))
	)
	for i, token := range tokens {
		balances[i], err = s.erc20.BalanceOf(ctx, common.HexToAddress(token), account)
		if err != nil {
			log.Printf("Fail to get balance: token=%s account=%v error=%v", token, account, err)
			return nil, fmt.Errorf("get balance: %w", err)
		}
	}

	return balances, nil
}

func (s *Service) getRates(
	ctx context.Context,
	tokens []string,
	decimals []uint8,
	amount *big.Int,
	minRate float64,
	maxRate float64,
) ([]float64, error) {
	rate, err := s.pricer.GetPrice(ctx, tokens[0], tokens[1], decimals[0], decimals[1], amount)
	if err != nil {
		return nil, err
	}

	if rate < minRate {
		rate = minRate
	} else if maxRate > 0 && rate > maxRate {
		rate = maxRate
	}

	rates := []float64{rate, 1 / rate}
	log.Printf("Get rate for tokens: %v\n", rates)

	return rates, nil
}

func (s *Service) getOrderID(key string) (string, error) {
	data, err := s.db.Get([]byte(key), nil)
	if err == nil {
		return string(data), nil
	}

	if errors.Is(err, leveldb.ErrNotFound) {
		return "", nil
	}

	return "", err
}

func (s *Service) checkAndCreateOrder(
	ctx context.Context,
	storageKey string,
	orderID string,
	account common.Address,
	tokens []string,
	decimals []uint8,
	rates []float64,
	startAmount *big.Int,
	minRate float64,
	maxRate float64,
) (string, error) {
	sellTokenIdx := 0
	sellAmount := startAmount
	if orderID != "" {
		order, err := s.cowClient.GetOrder(ctx, orderID)
		if err != nil {
			log.Printf("Fail to get order: key=%s id=%s error=%v\n", storageKey, orderID, err)
			return orderID, nil
		}

		// Order is still open, do nothing.
		if order.Status == orderStatusOpen {
			return orderID, nil
		}

		if strings.EqualFold(order.SellToken, tokens[1]) {
			sellTokenIdx = 1
		}

		if order.Status == orderStatusFulfilled {
			log.Printf("Order has been filled: key=%s\n", storageKey)
			sellTokenIdx = 1 - sellTokenIdx
			sellAmount = mustBigIntFromString(order.ExecutedBuyAmount)
		} else {
			log.Printf("Order has been cancelled or expired, update rates: key=%s\n", storageKey)
			sellAmount = mustBigIntFromString(order.SellAmount)
			amount := sellAmount
			if sellTokenIdx == 1 {
				amount = mustBigIntFromString(order.BuyAmount)
			}
			newRates, err := s.getRates(ctx, tokens, decimals, amount, minRate, maxRate)
			if err != nil {
				log.Printf("Fail to get rates: key=%s error=%v\n", storageKey, err)
				return "", err
			}
			copy(rates, newRates)
		}
	}

	// Create new order.
	buyAmount := convertAmountDecimals(
		scaleAmount(sellAmount, rates[sellTokenIdx]),
		decimals[sellTokenIdx], decimals[1-sellTokenIdx],
	)
	orderID, err := s.CreateOrder(ctx,
		account, tokens[sellTokenIdx], tokens[1-sellTokenIdx], sellAmount, buyAmount, "sell", false)
	if err != nil {
		log.Printf("Fail to create order: key=%s error=%v\n", storageKey, err)
		return "", err
	}

	// Store orderID into database.
	if err = s.db.Put([]byte(storageKey), []byte(orderID), nil); err != nil {
		log.Printf("Fail to store orderID: key=%s orderID=%s error=%v\n", storageKey, orderID, err)
	}

	return orderID, nil
}
