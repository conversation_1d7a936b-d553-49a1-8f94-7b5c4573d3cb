package service

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math/big"

	"github.com/KyberNetwork/tradingclients/pkg/routefinder"
)

func (s *Service) GetPrice(
	ctx context.Context,
	token1, token2 string,
	decimals1, decimals2 uint8,
	amount *big.Int,
) (float64, error) {
	params := routefinder.GetRouteParams{
		TokenIn:  token1,
		TokenOut: token2,
		AmountIn: amount.String(),
	}
	resp, err := s.routeFinderClient.GetRoute(ctx, params)
	if err != nil {
		return 0, fmt.Errorf("get route: %w", err)
	}

	amountOut, ok := new(big.Int).SetString(resp.RouteSummary.AmountOut, 10)
	if !ok {
		return 0, errors.New("invalid output amount")
	}

	params = routefinder.GetRouteParams{
		TokenIn:  token2,
		TokenOut: token1,
		AmountIn: amountOut.String(),
	}
	resp, err = s.routeFinderClient.GetRoute(ctx, params)
	if err != nil {
		return 0, fmt.Erro<PERSON>("get route: %w", err)
	}

	amountOut2, ok := new(big.Int).SetString(resp.RouteSummary.AmountOut, 10)
	if !ok {
		return 0, errors.New("invalid output amount")
	}

	amountOutF := common.WeiToFloat(amountOut, decimals2)
	sellRate := amountOutF / common.WeiToFloat(amount, decimals1)
	buyRate := amountOutF / common.WeiToFloat(amountOut2, decimals1)
	log.Printf("Calculate rate: sellRate=%f buyRate=%f rate=%f", sellRate, buyRate, (sellRate+buyRate)/2)

	return (sellRate + buyRate) / 2, nil
}
